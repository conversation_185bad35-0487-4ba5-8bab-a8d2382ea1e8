#!/usr/bin/env python3
"""
快速重置ChromaDB数据库脚本
提供多种重置策略，针对开发中频繁重置的需求进行优化

使用方法:
    python scripts/fast_reset_database.py [策略]
    
    策略选项:
    --fast      : 仅清空集合内容（最快）
    --recreate  : 删除并重建集合（推荐）
    --full      : 完全重建数据库（最彻底）
    --drop-data : 清空集合+删除data目录文件

使用场景:
    开发测试: --fast
    参数调整: --recreate  
    架构变更: --full
    完全清理: --drop-data
"""

import os
import sys
import time
import shutil
import argparse
from pathlib import Path
import chromadb

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from backend.config.settings import Settings as AppSettings
    settings = AppSettings()
except ImportError:
    print("警告: 无法导入项目配置，使用默认配置")
    class DefaultSettings:
        chroma_persist_directory = "./storage"
        collection_name = "documents" 
        data_dir = "./data"
    settings = DefaultSettings()


class FastDatabaseResetter:
    """快速数据库重置器"""
    
    def __init__(self):
        self.storage_path = Path(settings.chroma_persist_directory)
        self.data_path = Path(settings.data_dir)
        self.collection_name = settings.collection_name
        
    def reset_fast(self):
        """策略1: 快速清空 - 仅删除集合内容，保留结构"""
        print("🚀 执行快速清空（仅删除内容）...")
        start_time = time.time()
        
        try:
            client = chromadb.PersistentClient(path=str(self.storage_path))
            
            # 获取现有集合
            try:
                collection = client.get_collection(self.collection_name)
                
                # 获取所有ID并批量删除
                result = collection.get()
                if result["ids"]:
                    print(f"  📊 删除 {len(result['ids'])} 个文档...")
                    collection.delete(ids=result["ids"])
                    print("  ✅ 集合内容清空完成")
                else:
                    print("  ℹ️ 集合已经是空的")
                    
            except Exception as e:
                print(f"  ⚠️ 集合不存在或清空失败: {e}")
                # 尝试创建新集合
                try:
                    collection = client.create_collection(self.collection_name)
                    print("  ✅ 创建新集合")
                except Exception as create_e:
                    print(f"  ❌ 创建集合失败: {create_e}")
                    return False
                    
            elapsed = time.time() - start_time
            print(f"  ⏱️ 用时: {elapsed:.2f}秒")
            return True
            
        except Exception as e:
            print(f"❌ 快速清空失败: {e}")
            return False
    
    def reset_recreate_collection(self):
        """策略2: 重建集合 - 删除并重新创建集合（推荐）"""
        print("🔄 执行集合重建...")
        start_time = time.time()
        
        try:
            client = chromadb.PersistentClient(path=str(self.storage_path))
            
            # 删除现有集合
            try:
                client.delete_collection(self.collection_name)
                print(f"  🗑️ 删除现有集合: {self.collection_name}")
            except Exception as e:
                print(f"  ℹ️ 集合不存在，跳过删除: {e}")
            
            # 创建新集合
            collection = client.create_collection(
                name=self.collection_name,
                metadata={"description": "RAG文档向量存储集合", "reset_time": str(int(time.time()))}
            )
            print(f"  ✅ 创建新集合: {self.collection_name}")
            
            # 验证
            count = collection.count()
            print(f"  📊 新集合文档数: {count}")
            
            elapsed = time.time() - start_time  
            print(f"  ⏱️ 用时: {elapsed:.2f}秒")
            return True
            
        except Exception as e:
            print(f"❌ 集合重建失败: {e}")
            return False
    
    def reset_full_database(self):
        """策略3: 完全重建 - 删除整个数据库目录并重建"""
        print("💥 执行完全重建...")
        start_time = time.time()
        
        # 删除存储目录
        if self.storage_path.exists():
            print(f"  🗑️ 删除存储目录: {self.storage_path}")
            try:
                shutil.rmtree(self.storage_path)
            except Exception as e:
                print(f"  ❌ 删除失败: {e}")
                return False
        
        # 重新创建目录
        print(f"  📁 创建存储目录: {self.storage_path}")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        try:
            client = chromadb.PersistentClient(path=str(self.storage_path))
            collection = client.create_collection(
                name=self.collection_name,
                metadata={"description": "RAG文档向量存储集合", "rebuild_time": str(int(time.time()))}
            )
            print(f"  ✅ 初始化集合: {self.collection_name}")
            
        except Exception as e:
            print(f"  ❌ 初始化失败: {e}")
            return False
        
        elapsed = time.time() - start_time
        print(f"  ⏱️ 用时: {elapsed:.2f}秒")
        return True
    
    def reset_with_data_cleanup(self):
        """策略4: 清空集合+删除data目录文件"""
        print("🧹 执行深度清理（集合+文件）...")
        start_time = time.time()
        
        # 先重建集合
        if not self.reset_recreate_collection():
            return False
        
        # 清理data目录
        if self.data_path.exists():
            txt_files = list(self.data_path.glob("*.txt"))
            if txt_files:
                print(f"  🗑️ 删除data目录中的 {len(txt_files)} 个txt文件...")
                for txt_file in txt_files:
                    try:
                        txt_file.unlink()
                    except Exception as e:
                        print(f"    ⚠️ 删除失败 {txt_file.name}: {e}")
                print("  ✅ data目录清理完成")
            else:
                print("  ℹ️ data目录没有txt文件")
        else:
            print("  ℹ️ data目录不存在")
        
        elapsed = time.time() - start_time
        print(f"  ⏱️ 总用时: {elapsed:.2f}秒")
        return True
    
    def verify_reset(self):
        """验证重置结果"""
        print("\n🔍 验证重置结果...")
        
        try:
            client = chromadb.PersistentClient(path=str(self.storage_path))
            collection = client.get_collection(self.collection_name)
            
            count = collection.count()
            print(f"  📊 集合文档数: {count}")
            
            if count == 0:
                print("  ✅ 重置成功，集合为空")
            else:
                print(f"  ⚠️ 重置可能不完整，仍有 {count} 个文档")
            
            # 检查data目录
            if self.data_path.exists():
                txt_count = len(list(self.data_path.glob("*.txt")))
                print(f"  📁 data目录txt文件数: {txt_count}")
            
            return count == 0
            
        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
            return False
    
    def show_status(self):
        """显示当前状态"""
        print("📊 当前数据库状态:")
        
        try:
            client = chromadb.PersistentClient(path=str(self.storage_path))
            collections = client.list_collections()
            
            print(f"  🗂️ 集合数量: {len(collections)}")
            
            for coll in collections:
                print(f"    - {coll.name}: {coll.count()} 个文档")
            
            # 存储目录大小
            if self.storage_path.exists():
                size = sum(f.stat().st_size for f in self.storage_path.rglob('*') if f.is_file())
                size_mb = size / (1024 * 1024)
                print(f"  💾 存储大小: {size_mb:.2f}MB")
            
            # data目录状态
            if self.data_path.exists():
                txt_count = len(list(self.data_path.glob("*.txt")))
                print(f"  📁 data目录txt文件: {txt_count} 个")
            
        except Exception as e:
            print(f"  ❌ 状态查询失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="快速重置ChromaDB数据库")
    parser.add_argument("--fast", action="store_true", help="快速清空（仅删除内容）")
    parser.add_argument("--recreate", action="store_true", help="重建集合（推荐）")
    parser.add_argument("--full", action="store_true", help="完全重建数据库")
    parser.add_argument("--drop-data", action="store_true", help="清空集合+删除data文件")
    parser.add_argument("--status", action="store_true", help="仅显示当前状态")
    
    args = parser.parse_args()
    
    resetter = FastDatabaseResetter()
    
    # 显示当前状态
    if args.status:
        resetter.show_status()
        return
    
    print("🚀 ChromaDB快速重置工具")
    print("=" * 50)
    
    # 确定策略
    strategy = None
    if args.fast:
        strategy = "fast"
    elif args.recreate:
        strategy = "recreate"
    elif args.full:
        strategy = "full"  
    elif args.drop_data:
        strategy = "drop-data"
    else:
        # 交互式选择
        print("请选择重置策略:")
        print("  1. 快速清空（仅删除内容）- 最快")
        print("  2. 重建集合（推荐）- 平衡")
        print("  3. 完全重建 - 最彻底")
        print("  4. 深度清理（集合+文件）- 全清空")
        print("  5. 仅查看状态")
        
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            strategy_map = {
                "1": "fast",
                "2": "recreate", 
                "3": "full",
                "4": "drop-data",
                "5": "status"
            }
            strategy = strategy_map.get(choice)
        except KeyboardInterrupt:
            print("\n👋 操作已取消")
            return
    
    if strategy == "status":
        resetter.show_status()
        return
    
    if not strategy:
        print("❌ 无效选择")
        return
    
    # 显示当前状态
    resetter.show_status()
    
    # 确认操作
    print(f"\n⚠️ 即将执行 '{strategy}' 策略重置")
    try:
        confirm = input("确认继续? (y/N): ").lower().strip()
        if confirm != 'y':
            print("❌ 操作已取消")
            return
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
        return
    
    print()
    
    # 执行重置
    success = False
    if strategy == "fast":
        success = resetter.reset_fast()
    elif strategy == "recreate":
        success = resetter.reset_recreate_collection()
    elif strategy == "full":
        success = resetter.reset_full_database()
    elif strategy == "drop-data":
        success = resetter.reset_with_data_cleanup()
    
    # 验证结果
    if success:
        resetter.verify_reset()
        print("\n🎉 重置完成！")
        print("\n📝 接下来可以:")
        print("  1. 重启应用服务")
        print("  2. 使用API重新加载文档")
        print("  3. 运行ChestnutCMS同步")
    else:
        print("\n❌ 重置失败，请检查错误信息")


if __name__ == "__main__":
    main()