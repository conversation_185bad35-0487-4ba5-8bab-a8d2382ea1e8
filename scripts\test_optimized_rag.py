#!/usr/bin/env python3
"""
测试优化后的RAG配置
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings
from backend.app.services.rag_service import RAGService

def test_optimized_configuration():
    """测试优化配置的效果"""
    print("🔧 测试优化后的RAG配置")
    print("="*50)
    
    try:
        # 初始化RAG服务（现在使用优化配置）
        print("📚 初始化RAG服务...")
        rag_service = RAGService()
        print("✅ RAG服务初始化成功")
        
        # 检查优化配置是否生效
        from llama_index.core import Settings
        node_parser = Settings.node_parser
        
        print(f"\n🔍 当前配置检查:")
        print(f"  📏 chunk_size: {getattr(node_parser, 'chunk_size', 'N/A')}")
        print(f"  🔄 chunk_overlap: {getattr(node_parser, 'chunk_overlap', 'N/A')}")
        print(f"  📝 separator: {repr(getattr(node_parser, 'separator', 'N/A'))}")
        
        # 检查是否禁用了前后关系
        include_prev_next = getattr(node_parser, 'include_prev_next_rel', True)
        print(f"  🔗 include_prev_next_rel: {include_prev_next}")
        
        if not include_prev_next:
            print("  ✅ 前后关系已禁用，有助于减少复杂度")
        else:
            print("  ⚠️ 前后关系仍然启用")
        
        # 测试文档上传
        print(f"\n📤 测试文档上传...")
        test_content = """优化测试文档

这是一个用于测试优化配置的文档。

段落1：测试段落分隔符的效果。
这个段落应该被正确识别。

段落2：测试更大的chunk_size。
更大的chunk_size应该能减少文档块的总数。

段落3：测试减少的chunk_overlap。
较小的overlap应该能减少关系复杂度。

关键词：优化、配置、测试、效果
"""
        
        test_metadata = {
            "title": "优化测试文档",
            "content_id": "opt_test_001",
            "source": "optimization_test",
            "file_url": "https://example.com/opt_test"
        }
        
        result = rag_service.upload_document(
            test_content, 
            "optimization_test.txt", 
            test_metadata
        )
        
        if result["success"]:
            print("✅ 文档上传成功!")
            print(f"  📄 文件名: {result['filename']}")
            print(f"  🧩 新块数: {result['new_chunks']}")
            print(f"  📊 总块数: {result['total_chunks']}")
            
            # 比较块数（理论上应该更少）
            if result['new_chunks'] <= 3:
                print("  ✅ 块数较少，优化配置生效")
            else:
                print("  ⚠️ 块数较多，可能需要进一步优化")
        else:
            print(f"❌ 文档上传失败: {result['message']}")
            return False
        
        # 测试查询功能
        print(f"\n🔍 测试查询功能...")
        query_result = rag_service.query("优化配置的效果如何？")
        
        if query_result["success"]:
            print("✅ 查询成功!")
            print(f"  💬 回答: {query_result['answer'][:100]}...")
            print(f"  📚 源文档数: {query_result['total_sources']}")
            
            # 检查源文档的元数据
            if query_result['sources']:
                source = query_result['sources'][0]
                metadata = source.get('metadata', {})
                print(f"  🏷️ 源文档标题: {metadata.get('title')}")
                print(f"  🔗 源文档URL: {metadata.get('file_url')}")
        else:
            print(f"❌ 查询失败: {query_result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_configurations():
    """比较优化前后的配置差异"""
    print(f"\n📊 配置对比:")
    print(f"{'配置项':<20} {'优化前':<15} {'优化后':<15} {'说明'}")
    print("-" * 70)
    print(f"{'chunk_size':<20} {'512':<15} {'768':<15} {'减少总块数'}")
    print(f"{'chunk_overlap':<20} {'50':<15} {'30':<15} {'降低关系复杂度'}")
    separator_str = '\\n\\n'
    print(f"{'separator':<20} {'默认':<15} {separator_str:<15} {'段落分隔符'}")
    print(f"{'prev_next_rel':<20} {'True':<15} {'False':<15} {'禁用前后关系'}")
    
    print(f"\n💡 优化效果预期:")
    print(f"  • 文档块数量减少 ~30%")
    print(f"  • relationships 复杂度降低")
    print(f"  • 存储空间优化")
    print(f"  • 查询性能提升")

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📖 使用方法:")
    
    print(f"\n1. 自动应用（推荐）:")
    print(f"   RAG服务现在会自动使用优化配置")
    print(f"   无需额外操作，直接使用即可")
    
    print(f"\n2. 手动应用:")
    print(f"   from backend.config.rag_optimization import setup_optimized_node_parser")
    print(f"   setup_optimized_node_parser()  # 在初始化前调用")
    
    print(f"\n3. 获取优化的元数据字段:")
    print(f"   from backend.config.rag_optimization import get_optimized_metadata_keys")
    print(f"   essential_keys = get_optimized_metadata_keys()")
    
    print(f"\n4. 自定义优化:")
    print(f"   可以修改 backend/config/rag_optimization.py 中的参数")
    print(f"   根据具体需求调整 chunk_size、chunk_overlap 等")

def main():
    """主函数"""
    print("🚀 RAG优化配置测试")
    print("="*50)
    
    # 1. 显示使用方法
    show_usage_examples()
    
    # 2. 配置对比
    compare_configurations()
    
    # 3. 测试优化配置
    if test_optimized_configuration():
        print(f"\n🎉 优化配置测试成功!")
        print(f"✅ 系统已应用优化配置，性能得到提升")
    else:
        print(f"\n❌ 优化配置测试失败")
        print(f"⚠️ 请检查配置或查看错误日志")

if __name__ == "__main__":
    main()
