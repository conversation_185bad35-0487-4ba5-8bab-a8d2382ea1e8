# 诊断脚本使用指南

本目录包含了用于诊断和维护 ChromaDB 向量数据库的各种脚本工具。

## 📋 脚本列表

### 1. 基础检查脚本

#### `check_database.py`

**用途**: 检查数据库整体状态和文件一致性

```bash
python scripts/check_database.py
```

**检查内容**:

- ChromaDB 集合状态
- SQLite 数据库表结构
- 文档数量统计
- 文件一致性验证

#### `check_chromadb_storage.py`

**用途**: 深入分析 ChromaDB 存储结构

```bash
python scripts/check_chromadb_storage.py
```

**检查内容**:

- ChromaDB API 数据访问
- SQLite 存储结构分析
- 数据一致性验证
- \_node_content 结构解析

### 2. 问题诊断脚本

#### `diagnose_hnsw_error.py`

**用途**: 诊断 HNSW segment reader 错误

```bash
python scripts/diagnose_hnsw_error.py
```

**适用场景**:

- 查询时出现"HNSW segment reader"错误
- 向量检索失败
- 索引文件损坏

**检查内容**:

- 存储目录完整性
- SQLite 数据库状态
- ChromaDB 连接测试
- 向量文件检查
- 简单查询测试

#### `check_embedding_dimensions.py`

**用途**: 检查向量维度配置问题

```bash
python scripts/check_embedding_dimensions.py
```

**适用场景**:

- 维度不匹配错误
- 嵌入模型配置问题
- 向量存储异常

**检查内容**:

- 嵌入模型实际输出维度
- ChromaDB 集合期望维度
- 现有数据维度
- 修复建议

### 3. 文档管理脚本

#### `test_document_management.py`

**用途**: 测试文档管理功能

```bash
python scripts/test_document_management.py
```

#### `analyze_document_limits.py`

**用途**: 分析文档处理限制

```bash
python scripts/analyze_document_limits.py
```

### 4. 数据库重建脚本

#### `rebuild_database.py` ⭐ **强烈推荐**

**用途**: 完全重建 ChromaDB 数据库，解决各种数据库问题

```bash
python rebuild_database.py
```

**适用场景**:

- 数据库损坏或索引错误
- 向量维度不匹配问题
- HNSW segment reader 错误
- 数据不一致或丢失
- 性能下降需要优化
- 升级后的兼容性问题

**功能特点**:

- ✅ **自动备份**: 重建前自动备份现有数据库
- ✅ **安全操作**: 交互式确认，避免误操作
- ✅ **完整清理**: 彻底删除旧数据，避免残留问题
- ✅ **自动验证**: 重建后自动验证数据库完整性
- ✅ **详细日志**: 每步操作都有清晰的状态反馈

**操作流程**:

1. 显示当前配置信息
2. 用户确认操作（输入 y 继续）
3. 自动备份现有数据库到带时间戳的目录
4. 删除现有存储目录
5. 创建新的存储目录
6. 初始化全新的 ChromaDB 数据库
7. 验证数据库创建成功
8. 提供后续操作指导

**使用示例**:

```bash
$ python rebuild_database.py
🚀 开始重建ChromaDB数据库
==================================================
📋 配置信息:
  - 存储目录: ./storage
  - 集合名称: documents
  - 嵌入模型: text-embedding-3-small

⚠️  此操作将删除现有数据库，是否继续? (y/N): y

📦 备份现有存储目录到: ./storage_backup_1672531200
✅ 备份完成
🗑️  删除现有存储目录
📁 创建存储目录
🔧 初始化ChromaDB数据库
✅ 集合创建成功: documents
🔍 验证数据库
✅ 数据库验证完成

🎉 数据库重建完成！
📝 接下来的步骤:
  1. 重启应用服务
  2. 重新上传文档到data目录
  3. 点击'重新加载文档'按钮
```

**重要提示**:

- 重建后需要重新上传所有文档
- 备份文件会保留，可以手动删除
- 重建过程不可逆，请确保重要数据已备份

## 🔧 常见问题诊断流程

### 问题 1: 查询失败，出现 HNSW 错误

```bash
# 步骤1: 诊断HNSW问题
python scripts/diagnose_hnsw_error.py

# 步骤2: 检查维度配置
python scripts/check_embedding_dimensions.py

# 步骤3: 尝试重新加载文档
# 访问应用界面，点击"重新加载文档"

# 步骤4: 如果问题持续，使用重建脚本 (推荐)
python rebuild_database.py
```

### 问题 2: 数据不一致或丢失

```bash
# 步骤1: 检查数据库状态
python scripts/check_database.py

# 步骤2: 深入分析存储结构
python scripts/check_chromadb_storage.py

# 步骤3: 重建数据库 (最有效的解决方案)
python rebuild_database.py
```

### 问题 3: 向量维度不匹配

```bash
# 步骤1: 检查维度配置
python scripts/check_embedding_dimensions.py

# 步骤2: 如果配置正确但仍有问题，重建数据库
python rebuild_database.py

# 步骤3: 重建后重新上传文档
# 访问应用界面，重新上传所有文档
```

### 问题 4: 性能下降或响应缓慢 🐌

```bash
# 步骤1: 检查数据库状态和大小
python scripts/check_database.py

# 步骤2: 重建数据库优化性能 (推荐)
python rebuild_database.py

# 步骤3: 重新上传文档，获得最佳性能
```

### 问题 5: 系统升级后出现兼容性问题 🔄

```bash
# 推荐直接重建数据库，确保兼容性
python rebuild_database.py

# 然后重新上传所有文档
```

### 问题 6: 数据库文件过大或存储空间不足 💾

```bash
# 步骤1: 检查存储使用情况
python scripts/check_database.py

# 步骤2: 重建数据库压缩存储
python rebuild_database.py

# 重建后数据库会被优化，通常能显著减少存储空间
```

## 📊 脚本输出说明

### 状态标识

- ✅ **正常**: 检查通过，无问题
- ❌ **异常**: 发现问题，需要处理
- ⚠️ **警告**: 可能存在问题，建议关注
- 💡 **提示**: 建议或额外信息

### 常见输出解释

#### "text 字段为空，embedding 字段为 null"

**状态**: ✅ 正常
**说明**: 这是 ChromaDB 的正常设计，文本存储在 FTS 表中，向量存储在 embeddings 表中

#### "维度不匹配"

**状态**: ❌ 异常
**说明**: 嵌入模型输出维度与 ChromaDB 集合期望维度不一致
**解决**: 运行`check_embedding_dimensions.py`获取修复建议

#### "HNSW segment reader 错误"

**状态**: ❌ 异常
**说明**: 向量索引文件损坏或配置问题
**解决**: 重新加载文档或重建数据库

## 🛠️ 维护建议

### 定期检查 (建议每周)

```bash
# 基础健康检查
python scripts/check_database.py
```

### 问题排查 (出现问题时)

```bash
# 全面诊断
python scripts/diagnose_hnsw_error.py
python scripts/check_embedding_dimensions.py
python scripts/check_chromadb_storage.py

# 如果诊断发现问题，直接重建数据库 (最有效)
python rebuild_database.py
```

### 性能优化 (建议每月)

```bash
# 检查数据库大小和文档数量
python scripts/check_database.py

# 定期重建数据库优化性能 (强烈推荐)
python rebuild_database.py
```

### 系统维护最佳实践 ⭐

**`rebuild_database.py` 是解决大多数问题的万能工具**:

- 🔧 **遇到任何数据库问题时**: 优先考虑重建
- 📈 **性能下降时**: 重建可以显著提升性能
- 🔄 **系统升级后**: 重建确保兼容性
- 💾 **存储空间紧张时**: 重建可以压缩数据库
- 🛡️ **预防性维护**: 定期重建保持系统健康

**使用频率建议**:

- 🚨 **出现问题时**: 立即使用
- 📅 **定期维护**: 每月重建一次
- 🔄 **系统升级**: 升级后必须重建
- 📊 **性能优化**: 感觉变慢时重建

## 📝 日志和调试

所有脚本都会输出详细的检查过程和结果。如果需要保存日志：

```bash
# 保存检查结果到文件
python scripts/check_database.py > database_check.log 2>&1

# 保存诊断结果
python scripts/diagnose_hnsw_error.py > hnsw_diagnosis.log 2>&1
```

## 🆘 紧急修复

如果系统完全无法工作，**`rebuild_database.py` 是最佳选择**：

```bash
# 一键解决所有问题 (推荐)
python rebuild_database.py

# 脚本会自动：
# 1. 备份现有数据到带时间戳的目录
# 2. 完全重建数据库
# 3. 验证数据库完整性
# 4. 提供后续操作指导
```

**为什么 rebuild_database.py 如此有效？**

- ✅ 解决所有数据库相关问题
- ✅ 自动备份，安全可靠
- ✅ 重置所有配置到最佳状态
- ✅ 清理所有潜在的损坏数据
- ✅ 优化性能和存储空间

## 📞 获取帮助

如果脚本无法解决问题：

1. 查看脚本输出的详细错误信息
2. 检查应用日志
3. 参考项目文档 `docs/chromadb-storage-analysis.md`
4. 提交 Issue 并附上诊断脚本的输出结果
