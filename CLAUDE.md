# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在本代码库中工作时提供指导。

## 项目概览

基于 FastAPI + LlamaIndex 构建的 RAG 系统，集成 ChestnutCMS。系统自动从 MySQL 数据库同步文章，使用 ChromaDB 向量存储构建可搜索的知识库，提供 AI 问答接口。结合 BM25 关键词搜索和向量相似度搜索实现最优检索。

## 架构设计

### 服务层级结构
```
API路由 → ChestnutCMSService → MySQLService + RAGService → MySQL + ChromaDB
```

### 核心服务
- **RAGService** (`backend/app/services/rag_service.py`): LlamaIndex + ChromaDB 向量操作
- **MySQLService** (`backend/app/services/mysql_service.py`): 数据库连接层
- **ChestnutCMSService** (`backend/app/services/chestnut_cms_service.py`): 协调 CMS 与 RAG 之间的同步

### API 结构
```
/api/v1/
├── /query (POST) - RAG 查询
├── /documents/load (POST) - 从 data/ 加载
├── /sync/chestnut-cms (POST) - 同步 CMS 文章
└── /sync/status (GET) - 同步进度
```

## 常用命令

### 启动开发服务器
```bash
# 从项目根目录（推荐）
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload

# 快速启动（含依赖安装）
python start.py
```

### 数据库操作
```bash
# 修复常见的 HNSW 向量索引错误
python scripts/diagnose_hnsw_error.py
python scripts/rebuild_database.py  # 如果错误持续

# 测试 MySQL 连接
python test_mysql_connection.py

# 测试 CMS 同步功能
python test_chestnut_cms_sync.py
```

### 开发任务
```bash
# 安装依赖
pip install -r requirements.txt

# 运行测试（当添加后）
pytest tests/

# 检查嵌入维度
python scripts/check_embedding_dimensions.py
```

## 配置说明

### 必需的环境变量 (.env)
```env
# OpenAI 配置
OPENAI_API_KEY=your_key
OPENAI_BASE_URL=https://api.openai.com/v1  # 或代理
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# MySQL 数据库 (ChestnutCMS)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=chestnut_cms

# ChestnutCMS 基础 URL
CHESTNUT_CMS_BASE_URL=https://www.gzmdrw.cn
```

## 数据同步架构

### ChestnutCMS → RAG 流程
1. 查询 MySQL 表：`cms_content`、`cms_article_detail`、`cms_catalog`
2. 过滤条件：`link_flag IS NULL`
3. 使用 BeautifulSoup 清理 HTML（移除媒体、脚本、样式）
4. 构建文章 URL：`{base_url}{catalog_path}{content_id}.shtml`
5. 保存为 `data/{content_id}.txt` 并附加元数据
6. 向量化并存储到 ChromaDB

### 同步操作类型
- **新增**：CMS 中的新文章
- **更新**：`publish_date` 有变化
- **删除**：从 CMS 中移除的文章
- **保持**：未变化的文章

## 元数据结构

```python
{
    # 文件元数据
    "file_path": "data/{content_id}.txt",
    "file_name": "{content_id}.txt",
    
    # ChestnutCMS 元数据
    "content_id": "12345",
    "title": "文章标题",
    "file_url": "https://www.gzmdrw.cn/path/12345.shtml",
    "publish_date": "2024-01-01",
    "source": "chestnut_cms"
}
```

## 常见问题与解决方案

### HNSW Segment Reader 错误
```bash
# 表示向量索引损坏
python scripts/diagnose_hnsw_error.py
# 如果持续，重建数据库：
python scripts/rebuild_database.py
```

### 维度不匹配
```bash
# 检查当前配置
python scripts/check_embedding_dimensions.py
# 如需要，更新 .env 中的 EMBEDDING_MODEL
```

### MySQL 连接失败
1. 检查 `.env` 中的 MySQL 凭据
2. 验证 MySQL 服务正在运行
3. 运行 `python test_mysql_connection.py`
4. 检查网络/防火墙设置

### 查询结果为空
1. 验证 `data/` 目录中存在文档
2. 在界面中点击"重新加载文档"
3. 检查 ChromaDB 是否已索引文档
4. 查看后端日志中的错误

## 开发模式

### 服务初始化
服务在模块级别初始化为全局单例：
```python
# 在 services/__init__.py 中
rag_service = RAGService()
mysql_service = MySQLService()
```

### 错误处理
```python
# 服务层返回字典
try:
    return {"success": True, "data": result}
except Exception as e:
    logger.error(f"失败: {e}")
    return {"success": False, "message": str(e)}

# API 层抛出 HTTPException
if not result["success"]:
    raise HTTPException(400, detail=result["message"])
```

### 文档替换机制
文件通过文件名唯一标识。上传同名文件时：
1. 从 ChromaDB 删除所有旧块
2. 重新处理和索引新文件
3. 保留/更新元数据

## 测试注意事项

### 手动测试流程
1. 启动服务器：`python -m uvicorn backend.app.main:app --reload`
2. 访问 http://localhost:8000
3. 通过界面测试文档上传
4. 通过文档页面测试 CMS 同步
5. 验证聊天查询返回相关结果

### 关键测试场景
- 文档上传/替换/删除
- CMS 文章同步（新增/更新/删除）
- 使用不同措辞进行查询
- 响应中的元数据显示
- 同步期间的进度跟踪

## 文件导航指南

### 核心业务逻辑
- CMS 同步协调：`backend/app/services/chestnut_cms_service.py`
- RAG 操作：`backend/app/services/rag_service.py`
- 数据库查询：`backend/app/services/mysql_service.py`

### API 端点
- 查询处理：`backend/app/api/v1/query.py`
- 文档管理：`backend/app/api/v1/documents.py`
- CMS 同步：`backend/app/api/v1/cms_article_sync.py`

### 前端逻辑
- 聊天界面：`frontend/static/js/app.js`
- 文档管理：`frontend/static/js/documents.js`

### 配置
- 设置：`backend/config/settings.py`
- 环境变量：`.env`

## 性能优化

### 当前瓶颈
- 同步处理（考虑异步以扩展规模）
- 单线程文档处理
- 频繁查询无缓存层

### 优化机会
- 批量文档处理
- 查询结果缓存
- 异步 CMS 同步操作
- MySQL 连接池

## 部署注意事项

### 目录结构要求
```
project/
├── data/          # 文档存储（如缺失则创建）
├── storage/       # ChromaDB 向量（自动创建）
├── .env          # 配置（必需）
└── backend/      # 应用代码
```

### 健康检查
- `/api/v1/health/status` - 系统状态
- 监控项：ChromaDB、文档数量、配置

### 监控要点
- 文档处理时间
- 查询响应时间
- 同步操作持续时间
- 各端点错误率