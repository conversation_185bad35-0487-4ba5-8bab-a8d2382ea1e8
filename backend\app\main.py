"""
FastAPI主应用
提供RAG聊天服务的API接口
"""
import logging
import sys
import os
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.config.settings import settings
from backend.app.api.v1.api import api_router
from backend.app.core.events import create_start_app_handler, create_stop_app_handler
from backend.app.core.middleware import add_middlewares
from backend.app.core.exceptions import add_exception_handlers

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        openapi_url=f"{settings.api_v1_prefix}/openapi.json"
    )

    # 添加中间件
    add_middlewares(app)

    # 添加异常处理器
    add_exception_handlers(app)

    # 添加路由
    app.include_router(api_router, prefix=settings.api_v1_prefix)

    # 静态文件
    app.mount("/static", StaticFiles(directory="frontend/static"), name="static")

    # 应用事件
    app.add_event_handler("startup", create_start_app_handler())
    app.add_event_handler("shutdown", create_stop_app_handler())

    return app


# 创建应用实例
app = create_application()


# 页面路由


@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回聊天页面"""
    try:
        with open("frontend/templates/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>页面未找到</h1><p>请确保前端文件存在</p>",
            status_code=404
        )


@app.get("/documents", response_class=HTMLResponse)
async def documents_page():
    """返回文档管理页面"""
    try:
        with open("frontend/templates/documents.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>页面未找到</h1><p>文档管理页面不存在</p>",
            status_code=404
        )



if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=True,
        log_level="info"
    )

