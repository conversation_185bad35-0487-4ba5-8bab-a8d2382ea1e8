#!/usr/bin/env python3
"""
诊断元数据存储问题
"""
import os
import sys
import json
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings

def check_chromadb_metadata():
    """检查ChromaDB中的元数据问题"""
    print("🔍 检查ChromaDB元数据问题...")

    # 连接到ChromaDB的SQLite数据库
    db_path = settings.chroma_persist_directory / "chroma.sqlite3"
    if not db_path.exists():
        print("❌ ChromaDB数据库文件不存在")
        return False

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # 首先检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 数据库表: {[table[0] for table in tables]}")

        # 检查embedding_metadata表结构
        cursor.execute("PRAGMA table_info(embedding_metadata)")
        columns = cursor.fetchall()
        print(f"📋 embedding_metadata表结构:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")

        # 尝试不同的列名
        possible_columns = ['_node_content', 'node_content', 'metadata', 'string_value']
        node_content_column = None

        for col_name in possible_columns:
            try:
                cursor.execute(f"SELECT {col_name} FROM embedding_metadata LIMIT 1")
                node_content_column = col_name
                print(f"✅ 找到元数据列: {col_name}")
                break
            except sqlite3.OperationalError:
                continue

        if not node_content_column:
            print("❌ 未找到包含节点内容的列")
            return False

        # 检查元数据内容
        cursor.execute(f"SELECT {node_content_column} FROM embedding_metadata LIMIT 5")
        results = cursor.fetchall()
        
        print(f"📊 找到 {len(results)} 条元数据记录")
        
        for i, (node_content_json,) in enumerate(results, 1):
            print(f"\n📄 记录 {i}:")
            try:
                node_content = json.loads(node_content_json)
                
                # 检查各个问题
                print("🔍 问题检查:")
                
                # 1. file_size问题
                metadata = node_content.get("metadata", {})
                file_size = metadata.get("file_size")
                if file_size is None:
                    print("  ❌ file_size 为 None")
                else:
                    print(f"  ✅ file_size: {file_size} 字节")
                
                # 2. file_path问题
                file_path = metadata.get("file_path", "")
                if "\\\\" in file_path:
                    print(f"  ⚠️ file_path 包含双反斜杠: {file_path}")
                else:
                    print(f"  ✅ file_path: {file_path}")
                
                # 3. title Unicode问题
                title = metadata.get("title", "")
                if title and "\\u" in str(title):
                    print(f"  ❌ title 包含Unicode转义: {title}")
                    # 尝试解码
                    try:
                        decoded_title = title.encode().decode('unicode_escape')
                        print(f"      解码后: {decoded_title}")
                    except:
                        print("      无法解码")
                else:
                    print(f"  ✅ title: {title}")
                
                # 4. relationships复杂度
                relationships = node_content.get("relationships", {})
                rel_count = len(relationships)
                print(f"  📊 relationships 数量: {rel_count}")
                if rel_count > 0:
                    print(f"      关系类型: {list(relationships.keys())}")
                
                # 5. 其他字段
                text_length = len(node_content.get("text", ""))
                print(f"  📝 text 字段长度: {text_length}")
                
                start_idx = node_content.get("start_char_idx", 0)
                end_idx = node_content.get("end_char_idx", 0)
                print(f"  📍 字符索引: {start_idx} - {end_idx}")
                
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_file_metadata():
    """检查实际文件的元数据"""
    print("\n📁 检查实际文件元数据...")
    
    data_dir = Path(settings.data_dir)
    if not data_dir.exists():
        print("❌ 数据目录不存在")
        return False
    
    txt_files = list(data_dir.glob("*.txt"))
    print(f"📊 找到 {len(txt_files)} 个TXT文件")
    
    for file_path in txt_files[:3]:  # 只检查前3个
        print(f"\n📄 文件: {file_path.name}")
        try:
            stat = file_path.stat()
            print(f"  📏 文件大小: {stat.st_size} 字节")
            print(f"  📅 创建时间: {stat.st_ctime}")
            print(f"  📅 修改时间: {stat.st_mtime}")
            
            # 读取文件内容检查编码
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"  📝 内容长度: {len(content)} 字符")
                print(f"  🔤 前50字符: {content[:50]}...")
                
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    
    return True

def suggest_fixes():
    """建议修复方案"""
    print("\n🔧 修复建议:")
    print("1. file_size 问题:")
    print("   - 确保在创建Document时正确设置file_size")
    print("   - 检查SimpleDirectoryReader的元数据提取")
    
    print("\n2. Unicode 编码问题:")
    print("   - 确保字符串在存储前正确编码")
    print("   - 避免多次编码/解码")
    
    print("\n3. relationships 复杂度:")
    print("   - 这是LlamaIndex的内部机制，用于维护文档块关系")
    print("   - 可以通过配置减少不必要的关系")
    print("   - 考虑使用更简单的文档分块策略")
    
    print("\n4. 路径问题:")
    print("   - Windows路径中的双反斜杠是JSON转义的正常现象")
    print("   - 可以使用正斜杠或Path对象统一处理")

def main():
    """主函数"""
    print("🔍 ChromaDB元数据问题诊断")
    print("="*50)
    
    # 检查ChromaDB元数据
    if not check_chromadb_metadata():
        return
    
    # 检查文件元数据
    if not check_file_metadata():
        return
    
    # 提供修复建议
    suggest_fixes()
    
    print("\n✅ 诊断完成")

if __name__ == "__main__":
    main()
