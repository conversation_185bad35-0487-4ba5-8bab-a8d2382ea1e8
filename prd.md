# RAG 系统向量存储迁移 PRD

## 1. 项目背景

### 1.1 当前状况

本项目是一个基于 FastAPI + LlamaIndex 构建的 RAG（检索增强生成）聊天应用，目前使用 ChromaDB + SQLite 作为向量存储方案。系统具备以下特点：

- **技术栈**：FastAPI + LlamaIndex + ChromaDB + SQLite
- **存储方式**：本地 SQLite 文件存储向量数据
- **检索方式**：BM25 关键词检索 + 向量相似度检索的混合检索
- **向量模型**：text-embedding-3-small（1536 维）
- **数据规模**：支持文档上传、CMS 文章同步等功能

### 1.2 现有架构分析

```
当前架构：
FastAPI应用 → RAGService → ChromaVectorStore → ChromaDB → SQLite文件
```

**优势**：

- 部署简单，无需外部服务
- 数据本地化，便于备份
- 开发调试方便

**局限性**：

- 性能瓶颈：SQLite 在大规模向量检索时性能有限
- 扩展性差：难以支持分布式部署
- 功能受限：缺乏高级向量搜索功能
- 并发限制：SQLite 的并发写入能力有限

## 2. 迁移目标

### 2.1 业务目标

- **性能提升**：提高向量检索查询响应速度
- **扩展性增强**：为未来大规模数据和分布式部署做准备
- **功能丰富**：利用 Qdrant 的高级功能（如混合搜索、过滤等）
- **生态完善**：接入更成熟的向量数据库生态系统

### 2.2 技术目标

- **简化迁移**：直接删除旧数据库，重新构建向量索引
- **架构升级**：切换到 Qdrant 向量数据库
- **向后兼容**：保持现有 API 接口不变
- **性能基准**：查询响应时间不劣于现有系统

### 2.3 成功标准

- [ ] 所有现有功能正常工作
- [ ] 新系统能够正常重新索引文档
- [ ] 查询响应时间 ≤ 现有系统的 120%
- [ ] 系统稳定运行 7 天无异常
- [ ] 前端用户体验无变化

## 3. 技术方案

### 3.1 目标架构

```
新架构：
FastAPI应用 → RAGService → QdrantVectorStore → Qdrant服务器
```

### 3.2 核心变更

#### 3.2.1 依赖包变更

**新增依赖**：

```
qdrant-client>=1.7.0
llama-index-vector-stores-qdrant
```

**移除依赖**：

```
llama-index-vector-stores-chroma  # 不再需要
chromadb==1.0.12                 # 不再需要
```

#### 3.2.2 配置文件变更

**backend/config/settings.py**：

```python
# Qdrant配置
qdrant_host: str = "localhost"
qdrant_port: int = 6333
qdrant_grpc_port: int = 6334
qdrant_api_key: Optional[str] = None
qdrant_prefer_grpc: bool = True
```

**.env 文件**：

```env
# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_PREFER_GRPC=true
```

#### 3.2.3 服务类修改

**backend/app/services/rag_service.py**：

- 替换 ChromaDB 客户端为 Qdrant 客户端
- 保持 LlamaIndex 接口统一
- 移除 ChromaDB 相关代码

### 3.3 部署要求

#### 3.3.1 Qdrant 服务器部署

**Docker Compose 方式（推荐）**：

```yaml
version: "3.8"
services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    ports:
      - "6333:6333" # HTTP API
      - "6334:6334" # gRPC API
    volumes:
      - ./qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
```

**本地安装方式**：

```bash
# 使用Docker直接运行
docker run -p 6333:6333 -p 6334:6334 \
  -v $(pwd)/qdrant_data:/qdrant/storage \
  qdrant/qdrant:latest
```

## 4. 实施计划

### 4.1 阶段划分

#### 阶段 1：准备阶段（1-2 天）

**目标**：完成环境准备和方案设计
**任务**：

- [ ] 研究 Qdrant 部署最佳实践
- [ ] 准备 Docker Compose 配置文件
- [ ] 确认直接删除旧数据库的策略
- [ ] 更新项目文档

**交付物**：

- Docker Compose 配置文件
- 数据库删除和重建方案文档
- 更新的 README.md

#### 阶段 2：开发阶段（2-3 天）

**目标**：完成代码修改和工具开发
**任务**：

- [ ] 修改配置文件（settings.py, .env）
- [ ] 更新 RAGService 支持 Qdrant
- [ ] 编写数据库清理脚本
- [ ] 更新诊断和维护脚本
- [ ] 更新 requirements.txt

**交付物**：

- 修改后的配置文件
- 更新的 RAGService 代码
- 数据库清理脚本
- 更新的诊断脚本

## 5. 简化迁移策略

### 5.1 迁移方式

**直接替换策略**：

1. **停止服务**：停止 FastAPI 应用
2. **删除旧数据**：直接删除`./storage`目录下的所有 ChromaDB 文件
3. **启动 Qdrant**：启动 Qdrant 服务器
4. **更新代码**：切换到新的 Qdrant 配置
5. **重新索引**：重新加载所有文档，自动重建向量索引
6. **启动服务**：启动更新后的 FastAPI 应用

### 5.2 数据处理

- **不保留旧向量数据**：直接删除，重新生成
- **保留原始文档**：`./data`目录下的 TXT 文件保持不变
- **重新索引**：利用现有的文档加载功能重建向量索引
- **CMS 数据**：可通过 CMS 同步功能重新获取

### 5.3 优势

- **实施简单**：无需复杂的数据迁移脚本
- **风险较低**：原始文档文件不变，可随时重新索引
- **清理彻底**：完全移除 ChromaDB 依赖
- **测试容易**：可在测试环境快速验证

### 5.4 注意事项

- **服务中断**：需要短暂停机（预计 10-30 分钟）
- **重新索引时间**：根据文档数量，可能需要几分钟到几十分钟
- **备份建议**：虽然可重新生成，但建议备份`./data`目录
