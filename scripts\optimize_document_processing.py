#!/usr/bin/env python3
"""
优化文档处理，减少relationships复杂度和存储冗余
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings
from backend.app.services.rag_service import RAGService

def optimize_node_parser_settings():
    """优化NodeParser设置，减少relationships复杂度"""
    print("🔧 优化NodeParser设置...")
    
    # 建议的优化配置
    optimized_config = {
        "chunk_size": 512,
        "chunk_overlap": 50,
        "separator": "\n\n",  # 使用段落分隔符
        "include_metadata": True,
        "include_prev_next_rel": False,  # 禁用前后关系
    }
    
    print("📋 建议的优化配置:")
    for key, value in optimized_config.items():
        print(f"  - {key}: {value}")
    
    return optimized_config

def create_optimized_rag_service():
    """创建优化的RAG服务配置"""
    print("\n🔧 创建优化的RAG服务配置...")
    
    # 这里我们可以创建一个配置文件或者修改现有的设置
    optimization_suggestions = [
        "1. 减少chunk_overlap以降低关系复杂度",
        "2. 使用更大的chunk_size减少总块数",
        "3. 禁用不必要的节点关系",
        "4. 优化元数据存储策略",
        "5. 考虑使用更简单的分块策略"
    ]
    
    print("💡 优化建议:")
    for suggestion in optimization_suggestions:
        print(f"  {suggestion}")
    
    return optimization_suggestions

def analyze_storage_efficiency():
    """分析存储效率"""
    print("\n📊 分析存储效率...")
    
    try:
        import chromadb
        client = chromadb.PersistentClient(path=str(settings.chroma_persist_directory))
        collection = client.get_collection(settings.collection_name)
        
        total_docs = collection.count()
        
        # 获取数据库文件大小
        db_path = settings.chroma_persist_directory / "chroma.sqlite3"
        if db_path.exists():
            db_size_mb = db_path.stat().st_size / (1024 * 1024)
            print(f"📏 数据库大小: {db_size_mb:.2f} MB")
            print(f"📄 文档数量: {total_docs}")
            print(f"📊 平均每文档: {db_size_mb/total_docs:.2f} MB")
        
        # 分析元数据冗余
        result = collection.get(include=["metadatas"])
        if result['metadatas']:
            metadata_keys = set()
            for metadata in result['metadatas']:
                metadata_keys.update(metadata.keys())
            
            print(f"🏷️ 唯一元数据字段数: {len(metadata_keys)}")
            print(f"🔄 总元数据记录数: {len(result['metadatas'])}")
            print(f"📈 冗余比例: {len(result['metadatas']) / len(metadata_keys):.1f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def suggest_improvements():
    """建议改进方案"""
    print("\n💡 改进建议:")
    
    improvements = [
        {
            "问题": "relationships 复杂度高",
            "解决方案": [
                "配置 SentenceSplitter 禁用前后关系",
                "使用更大的 chunk_size 减少块数",
                "考虑使用 SimpleNodeParser 替代"
            ]
        },
        {
            "问题": "元数据存储冗余",
            "解决方案": [
                "这是 ChromaDB 的正常行为",
                "元数据被索引以支持快速查询",
                "可以通过减少元数据字段来优化"
            ]
        },
        {
            "问题": "存储空间占用",
            "解决方案": [
                "定期清理不需要的文档",
                "优化分块策略",
                "考虑使用压缩存储"
            ]
        }
    ]
    
    for i, improvement in enumerate(improvements, 1):
        print(f"\n{i}. {improvement['问题']}:")
        for solution in improvement['解决方案']:
            print(f"   • {solution}")

def create_optimized_config():
    """创建优化配置文件"""
    print("\n📝 创建优化配置...")
    
    config_content = '''# RAG服务优化配置
# 减少relationships复杂度和存储冗余

from llama_index.core.node_parser import SentenceSplitter
from llama_index.core import Settings

def setup_optimized_node_parser():
    """设置优化的节点解析器"""
    Settings.node_parser = SentenceSplitter(
        chunk_size=768,  # 增大块大小减少总块数
        chunk_overlap=30,  # 减少重叠降低关系复杂度
        separator="\\n\\n",  # 使用段落分隔符
        include_metadata=True,
        include_prev_next_rel=False,  # 禁用前后关系
    )

def get_optimized_metadata_keys():
    """获取优化的元数据字段列表"""
    # 只保留必要的元数据字段
    essential_keys = [
        "filename",
        "content_id", 
        "title",
        "source",
        "file_url"
    ]
    return essential_keys
'''
    
    config_path = project_root / "backend" / "config" / "rag_optimization.py"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 优化配置已保存到: {config_path}")
    return config_path

def main():
    """主函数"""
    print("🔧 RAG文档处理优化分析")
    print("="*50)
    
    # 1. 优化NodeParser设置
    optimize_node_parser_settings()
    
    # 2. 创建优化的RAG服务配置
    create_optimized_rag_service()
    
    # 3. 分析存储效率
    analyze_storage_efficiency()
    
    # 4. 建议改进方案
    suggest_improvements()
    
    # 5. 创建优化配置文件
    create_optimized_config()
    
    print("\n✅ 优化分析完成")
    print("\n📋 总结:")
    print("1. 当前系统功能正常，主要是存储效率问题")
    print("2. relationships 是 LlamaIndex 的设计特性，可以通过配置优化")
    print("3. 元数据冗余是 ChromaDB 的正常行为，支持快速查询")
    print("4. 建议使用提供的优化配置减少复杂度")

if __name__ == "__main__":
    main()
