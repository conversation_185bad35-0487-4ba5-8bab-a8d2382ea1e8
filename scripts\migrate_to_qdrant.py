#!/usr/bin/env python3
"""
ChromaDB到Qdrant迁移脚本
删除旧的ChromaDB数据，为Qdrant迁移做准备

使用方法:
    python scripts/migrate_to_qdrant.py [--backup] [--force]

参数:
    --backup: 在删除前备份ChromaDB数据
    --force: 强制执行，不询问确认
"""

import os
import sys
import shutil
import argparse
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from backend.config.settings import Settings as AppSettings
    settings = AppSettings()
except ImportError:
    print("警告: 无法导入项目配置，使用默认配置")
    class DefaultSettings:
        chroma_persist_directory = "./storage"
        data_dir = "./data"
    settings = DefaultSettings()


class ChromaToQdrantMigrator:
    """ChromaDB到Qdrant迁移器"""
    
    def __init__(self, backup=False, force=False):
        self.backup = backup
        self.force = force
        self.storage_path = Path(settings.chroma_persist_directory)
        self.data_path = Path(settings.data_dir)
        
    def run_migration(self):
        """执行迁移"""
        print("🚀 ChromaDB到Qdrant迁移工具")
        print("=" * 50)
        
        # 显示配置信息
        self._show_config()
        
        # 确认操作
        if not self.force and not self._confirm_migration():
            print("❌ 迁移已取消")
            return False
        
        print()
        
        # 步骤1: 备份数据（如果需要）
        if self.backup:
            if not self._backup_chromadb():
                print("❌ 迁移失败：备份失败")
                return False
        
        # 步骤2: 删除ChromaDB数据
        if not self._delete_chromadb():
            print("❌ 迁移失败：删除ChromaDB数据失败")
            return False
        
        # 步骤3: 验证数据目录
        if not self._verify_data_directory():
            print("❌ 迁移失败：数据目录验证失败")
            return False
        
        # 步骤4: 显示后续步骤
        self._show_next_steps()
        
        print("✅ ChromaDB数据清理完成！")
        print("📝 请按照上述步骤完成Qdrant迁移")
        return True
    
    def _show_config(self):
        """显示配置信息"""
        print(f"📋 配置信息:")
        print(f"  - ChromaDB存储目录: {self.storage_path}")
        print(f"  - 数据目录: {self.data_path}")
        print(f"  - 备份模式: {'是' if self.backup else '否'}")
        print(f"  - 强制模式: {'是' if self.force else '否'}")
        print()
    
    def _confirm_migration(self):
        """确认迁移操作"""
        print("⚠️  此操作将:")
        print("   1. 删除所有ChromaDB向量数据")
        print("   2. 保留原始文档文件（data目录）")
        print("   3. 需要重新索引所有文档")
        print()
        
        confirm = input("确认继续迁移? (y/N): ").lower().strip()
        return confirm == 'y'
    
    def _backup_chromadb(self):
        """备份ChromaDB数据"""
        print("📦 备份ChromaDB数据...")
        
        if not self.storage_path.exists():
            print("ℹ️  ChromaDB存储目录不存在，跳过备份")
            return True
        
        try:
            # 创建备份目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = Path(f"backup_chromadb_{timestamp}")
            backup_dir.mkdir(exist_ok=True)
            
            # 复制存储目录
            backup_storage = backup_dir / "storage"
            shutil.copytree(self.storage_path, backup_storage)
            
            print(f"✅ ChromaDB数据已备份到: {backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def _delete_chromadb(self):
        """删除ChromaDB数据"""
        print("🗑️  删除ChromaDB数据...")
        
        if not self.storage_path.exists():
            print("ℹ️  ChromaDB存储目录不存在，无需删除")
            return True
        
        try:
            # 删除存储目录
            shutil.rmtree(self.storage_path)
            print(f"✅ 已删除ChromaDB存储目录: {self.storage_path}")
            
            # 重新创建空目录
            self.storage_path.mkdir(exist_ok=True)
            print(f"✅ 已重新创建存储目录: {self.storage_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 删除ChromaDB数据失败: {e}")
            return False
    
    def _verify_data_directory(self):
        """验证数据目录"""
        print("📁 验证数据目录...")
        
        if not self.data_path.exists():
            print(f"⚠️  数据目录不存在: {self.data_path}")
            print("   创建空数据目录...")
            self.data_path.mkdir(exist_ok=True)
        
        # 统计TXT文件
        txt_files = list(self.data_path.glob("*.txt"))
        print(f"✅ 数据目录验证完成")
        print(f"   - 路径: {self.data_path}")
        print(f"   - TXT文件数量: {len(txt_files)}")
        
        if txt_files:
            print("   - 文件列表:")
            for txt_file in txt_files[:5]:  # 只显示前5个
                print(f"     * {txt_file.name}")
            if len(txt_files) > 5:
                print(f"     ... 还有 {len(txt_files) - 5} 个文件")
        
        return True
    
    def _show_next_steps(self):
        """显示后续步骤"""
        print()
        print("📝 后续步骤:")
        print("   1. 确保Qdrant服务器正在运行:")
        print("      docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest")
        print()
        print("   2. 安装新的依赖包:")
        print("      pip install qdrant-client llama-index-vector-stores-qdrant")
        print()
        print("   3. 启动应用程序:")
        print("      python backend/app/main.py")
        print()
        print("   4. 重新加载文档:")
        print("      访问 http://localhost:8000/docs 使用 /documents/load 接口")
        print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ChromaDB到Qdrant迁移工具")
    parser.add_argument("--backup", action="store_true", help="在删除前备份ChromaDB数据")
    parser.add_argument("--force", action="store_true", help="强制执行，不询问确认")
    
    args = parser.parse_args()
    
    migrator = ChromaToQdrantMigrator(backup=args.backup, force=args.force)
    success = migrator.run_migration()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
