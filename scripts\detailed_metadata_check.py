#!/usr/bin/env python3
"""
详细检查ChromaDB元数据内容
"""
import os
import sys
import json
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings

def check_detailed_metadata():
    """详细检查元数据内容"""
    print("🔍 详细检查ChromaDB元数据...")
    
    db_path = settings.chroma_persist_directory / "chroma.sqlite3"
    if not db_path.exists():
        print("❌ ChromaDB数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查看所有元数据记录
        cursor.execute("SELECT key, string_value FROM embedding_metadata WHERE string_value IS NOT NULL")
        results = cursor.fetchall()
        
        print(f"📊 找到 {len(results)} 条元数据记录")
        
        for i, (key, string_value) in enumerate(results, 1):
            print(f"\n📄 记录 {i} (key: {key}):")
            print(f"原始值: {string_value[:200]}...")
            
            # 尝试解析JSON
            try:
                if string_value.startswith('{'):
                    data = json.loads(string_value)
                    print("✅ JSON解析成功")
                    
                    # 检查是否是TextNode
                    if data.get('class_name') == 'TextNode':
                        print("📝 这是一个TextNode")
                        
                        # 检查元数据
                        metadata = data.get('metadata', {})
                        print(f"🏷️ 元数据字段: {list(metadata.keys())}")
                        
                        # 检查具体问题
                        if 'file_size' in metadata:
                            print(f"  📏 file_size: {metadata['file_size']}")
                        
                        if 'title' in metadata:
                            title = metadata['title']
                            print(f"  📰 title (原始): {repr(title)}")
                            if isinstance(title, str) and '\\u' in title:
                                try:
                                    decoded = title.encode('utf-8').decode('unicode_escape')
                                    print(f"  📰 title (解码): {decoded}")
                                except:
                                    print("  ❌ title 解码失败")
                        
                        if 'file_path' in metadata:
                            print(f"  📁 file_path: {metadata['file_path']}")
                        
                        # 检查relationships
                        relationships = data.get('relationships', {})
                        if relationships:
                            print(f"  🔗 relationships: {len(relationships)} 个关系")
                            for rel_key, rel_data in relationships.items():
                                print(f"    - {rel_key}: {rel_data.get('node_type', 'unknown')}")
                        
                        # 检查text字段
                        text = data.get('text', '')
                        print(f"  📝 text长度: {len(text)}")
                        
                else:
                    print(f"⚠️ 非JSON数据: {string_value}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   原始数据: {repr(string_value[:100])}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_chroma_api():
    """使用ChromaDB API检查数据"""
    print("\n🔍 使用ChromaDB API检查数据...")
    
    try:
        import chromadb
        client = chromadb.PersistentClient(path=str(settings.chroma_persist_directory))
        collection = client.get_collection(settings.collection_name)
        
        print(f"📊 集合文档数量: {collection.count()}")
        
        # 获取所有数据
        result = collection.get(include=["documents", "metadatas", "embeddings"])
        
        print(f"📄 实际获取到 {len(result['ids'])} 个文档")
        
        for i, (doc_id, document, metadata) in enumerate(zip(
            result['ids'], result['documents'], result['metadatas']
        )):
            print(f"\n📄 文档 {i+1}:")
            print(f"  🆔 ID: {doc_id}")
            print(f"  📝 内容长度: {len(document) if document else 0}")
            print(f"  📝 内容预览: {document[:100] if document else 'None'}...")
            
            if metadata:
                print(f"  🏷️ 元数据字段: {list(metadata.keys())}")
                
                # 检查具体字段
                if 'title' in metadata:
                    title = metadata['title']
                    print(f"    📰 title: {title}")
                
                if 'file_size' in metadata:
                    print(f"    📏 file_size: {metadata['file_size']}")
                
                if 'file_path' in metadata:
                    print(f"    📁 file_path: {metadata['file_path']}")
        
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB API检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 详细元数据检查")
    print("="*50)
    
    # 检查SQLite数据
    check_detailed_metadata()
    
    # 检查ChromaDB API数据
    check_chroma_api()
    
    print("\n✅ 检查完成")

if __name__ == "__main__":
    main()
