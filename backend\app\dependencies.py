"""
依赖注入模块
"""
import logging
from typing import Generator
from fastapi import Depends, HTTPException, status

from backend.app.services.rag_service import RAGService

logger = logging.getLogger(__name__)

# 全局服务实例
_rag_service: RAGService = None


async def get_rag_service() -> RAGService:
    """获取RAG服务实例"""
    global _rag_service
    if _rag_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="RAG服务未初始化"
        )
    return _rag_service


async def initialize_services():
    """初始化所有服务"""
    global _rag_service
    try:
        logger.info("正在初始化RAG服务...")
        _rag_service = RAGService()
        logger.info("RAG服务初始化完成")
    except Exception as e:
        logger.error(f"RAG服务初始化失败: {e}")
        raise


async def cleanup_services():
    """清理所有服务"""
    global _rag_service
    if _rag_service:
        logger.info("正在清理RAG服务...")
        _rag_service = None
        logger.info("RAG服务清理完成")
